## 代理 IP 使用说明
> 还是得跟大家再次强调下，不要对一些自媒体平台进行大规模爬虫或其他非法行为，要踩缝纫机的哦🤣  
> 另外如果要是用代理功能，请安装Redis并设置一个密码，从下面的流程图讲解了redis在这个缓存功能中起到的作用
### 简易的流程图

![代理 IP 使用流程图](../static/images/代理IP%20流程图.drawio.png)

### 准备代理 IP 信息
点击 <a href="https://www.jisuhttp.com/?pl=mAKphQ&plan=ZY&kd=Yang">极速HTTP代理</a> 官网注册并实名认证（国内使用代理 IP 必须要实名，懂的都懂）

### 获取 IP 提取链接
> 每个人注册并实名认证后都会送一定的余额。（当然有些网站上也有一些免费的IP，但失效时间极快，也体验过一些免费的 IP 代理池，轮询去找一个可用IP都得半天）

在IP提取页面点击生成 API 链接，这样就会生成一个跟你账号相关的IP提取的链接，其中我们只需要关注2个参数<br>
`key`、`crypto`，比如下面这张图中`key=w3q**********` `crypto=2f945*********`

![img.png](../static/images/IP_提取图.png)

### 将提取密钥参数 key crypto 写入环境变量
> 或者直接在代码中填写 `key` 和 `crypto` 的值

![img_1.png](../static/images/修改代理密钥.png)


### 将配置文件中的`ENABLE_IP_PROXY`置为 `True`
> `IP_PROXY_POOL_COUNT` 池子中 IP 的数量


### 其他说明
> 代理IP池使用了redis来缓存IP和记录过期时间
> 使用 <a href="https://sider.ai/invited?c=8e03db1a973401fdf114ed9cf9f8c183">chatgpt</a> 快速询问如何安装 redis 并设置密码



